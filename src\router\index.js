import { createRouter } from "vue-router"
import { routerConfig } from "@/router/config"
import { registerNavigationGuard } from "@/router/guard"
import { flatMultiLevelRoutes } from "./helper"

const Layouts = () => import("@/layouts/index.vue")

/**
 * @name 常驻路由
 * @description 除了 redirect/403/404/login 等隐藏页面，其他页面建议设置唯一的 Name 属性
 */
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layouts,
    meta: {
      hidden: true,
    },
    children: [
      {
        path: ":path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/403",
    component: () => import("@/views/error/403.vue"),
    meta: {
      hidden: true,
    },
  },
  {
    path: "/404",
    component: () => import("@/views/error/404.vue"),
    meta: {
      hidden: true,
    },
    alias: "/:pathMatch(.*)*",
  },
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      hidden: true,
    },
  }
];

/**
 * @name 动态路由
 * @description 用来放置有权限 (Roles 属性) 的路由
 * @description 必须带有唯一的 Name 属性
 */
export const dynamicRoutes = [
  {
    path: "/questionSupervise",
    component: Layouts,
    name: "QuestionSupervise",
    meta: {
      title: "问题督办",
      svgIcon: "question-supervise",
      roles: ["provincial"],
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/admin/question-supervise/index.vue"),
        name: "QuestionSuperviseIndex",
        meta: {
          title: "问题督办",
          svgIcon: "question-supervise",
          roles: ["provincial"],
          firstMenuFor: ["provincial"],
        },
      },
    ],
  },
  {
    path: "/complaintDistribution",
    component: Layouts,
    name: "ComplaintDistribution",
    meta: {
      title: "投诉分办",
      svgIcon: "complaint-distribution",
      roles: ["provincial"],
    },
    children: [
      {
        path: "index",
        component: () =>
          import("@/views/admin/complaint-distribution/index.vue"),
        name: "ComplaintDistributionIndex",
        meta: {
          title: "投诉分办",
          svgIcon: "complaint-distribution",
          roles: ["provincial"],
          firstMenuFor: ["municipal"],
        },
      },
    ],
  },
  {
    path: "/pendingQuestion",
    component: Layouts,
    name: "PendingQuestion",
    meta: {
      title: "待处理问题",
      svgIcon: "pending-question",
      roles: ["provincial"],
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/admin/pending-question/index.vue"),
        name: "PendingQuestionIndex",
        meta: {
          title: "待处理问题",
          svgIcon: "pending-question",
          roles: ["provincial"],
          firstMenuFor: ["municipal"],
        },
      },
    ],
  },
  {
    path: "/historyRecord",
    component: Layouts,
    name: "HistoryRecord",
    meta: {
      title: "历史处置记录",
      svgIcon: "history-record",
      roles: ["provincial"],
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/admin/history-record/index.vue"),
        name: "HistoryRecordIndex",
        meta: {
          title: "历史处置记录",
          svgIcon: "history-record",
          roles: ["provincial"],
          firstMenuFor: ["municipal"],
        },
      },
    ],
  },
  {
    path: "/dataBoard",
    component: Layouts,
    name: "DataBoard",
    meta: {
      title: "数据看板",
      svgIcon: "data-board",
      roles: ["provincial"],
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/admin/data-board/index.vue"),
        name: "DataBoardIndex",
        meta: {
          title: "数据看板",
          svgIcon: "data-board",
          roles: ["provincial"],
          firstMenuFor: ["municipal"],
        },
      },
    ],
  },
  {
    path: "/system",
    component: Layouts,
    name: "System",
    meta: {
      title: "系统管理",
      svgIcon: "system",
      roles: ["provincial"],
    },
    children: [
      {
        path: "user",
        component: () => import("@/views/admin/system/user/index.vue"),
        name: "User",
        meta: {
          title: "用户管理",
          roles: ["provincial"],
          firstMenuFor: ["municipal"],
        },
      },
      {
        path: "school",
        component: () => import("@/views/admin/system/school/index.vue"),
        name: "School",
        meta: {
          title: "学校管理",
          roles: ["provincial"],
          firstMenuFor: ["municipal"],
        },
      },
    ],
  },
];

/** 路由实例 */
export const router = createRouter({
  history: routerConfig.history,
  routes: routerConfig.thirdLevelRouteCache ? flatMultiLevelRoutes(constantRoutes) : constantRoutes
})

/** 重置路由 */
export function resetRouter() {
  try {
    // 注意：所有动态路由路由必须带有 Name 属性，否则可能会不能完全重置干净
    router.getRoutes().forEach((route) => {
      const { name, meta } = route
      if (name && meta.roles?.length) {
        router.hasRoute(name) && router.removeRoute(name)
      }
    })
  } catch {
    // 强制刷新浏览器也行，只是交互体验不是很好
    location.reload()
  }
}

// 注册路由导航守卫
registerNavigationGuard(router)

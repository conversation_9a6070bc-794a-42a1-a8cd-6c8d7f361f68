/* 主题颜色 */
:root {
  --blue1: #0055ff;
  --blue2: #3381ff;
  --blue3: #6e4aff;
  --yellow1: #edd00f;
  --grey1: #2b2c33;
  --grey2: #b4b6be;
  --grey3: #91939e;
  --grey4: #d8d8d8;
  --grey5: #6d6f75;
  --red1: #ea4c42;
  --red2: #e72c4a;
  --green1: #28c94f;
  --green2: #28c94f;
  --line: #e2e3e6;
  --border-1: #e6e6e6;
  --custom-blue-button-hover-color: #ebf5ff;
  --custom-green-button-hover-color: #eafaf2;
  --custom-red-button-hover-color: #fff2f4;
  --box-bg-color: #ffffff;
  --wait-status-bg-color: #f2fbff;
  --pass-status-bg-color: #f3fffa;
  --reject-status-bg-color: #fff2f4;
}

.blue1 {
  color: var(--blue1);
}

.blue2 {
  color: var(--blue2);
}

.blue3 {
  color: var(--blue3);
}

.yellow1 {
  color: var(--yellow1);
}

.grey1 {
  color: var(--grey1);
}

.grey2 {
  color: var(--grey2);
}

.grey3 {
  color: var(--grey3);
}

.grey4 {
  color: var(--grey4);
}

.grey5 {
  color: var(--grey5);
}

.red1 {
  color: var(--red1);
}

.red2 {
  color: var(--red2);
}

.green1 {
  color: var(--green1);
}

.green2 {
  color: var(--green2);
}

.white {
  color: #ffffff;
}

/* 鼠标cursor */
.pointer {
  cursor: pointer;
}

.not-allowed {
  cursor: not-allowed;
}

/* 相关字体 */
.text-20-28-600 {
  font-size: 20px;
  line-height: 28px;
  font-weight: 600;
}

.text-24-32-400 {
  font-size: 24px;
  line-height: 32px;
  font-weight: 400;
}

.text-18-26-600 {
  font-size: 18px;
  line-height: 26px;
  font-weight: 600;
}

.text-18-26-400 {
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
}

.text-16-24-600 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
}

.text-16-24-400 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
}

.text-14-20-600 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
}

.text-14-20-400 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.text-12-18-600 {
  font-size: 12px;
  line-height: 18px;
  font-weight: 600;
}

.text-12-18-400 {
  font-size: 12px;
  line-height: 18px;
  font-weight: 400;
}

@for $i from 12 through 30 {
  .text_#{$i} {
    font-size: #{$i}px;
  }
}

/* 全局间距 */
// 循环创建全局变量 形如 mb ml mr mt
@for $i from 0 through 60 {
  .m#{$i} {
    margin: #{$i}px !important;
  }
  .mt#{$i} {
    margin-top: #{$i}px !important;
  }
  .mr#{$i} {
    margin-right: #{$i}px !important;
  }
  .mb#{$i} {
    margin-bottom: #{$i}px !important;
  }
  .ml#{$i} {
    margin-left: #{$i}px !important;
  }
  .p#{$i} {
    padding: #{$i}px !important;
  }
  .pt#{$i} {
    padding-top: #{$i}px !important;
  }
  .pr#{$i} {
    padding-right: #{$i}px !important;
  }
  .pb#{$i} {
    padding-bottom: #{$i}px !important;
  }
  .pl#{$i} {
    padding-left: #{$i}px !important;
  }
}

/* 常用布局 */
/* flex */
.flex-start-start {
  display: flex;
  justify-content: flex-start !important;
  align-items: flex-start !important;
}

.flex-start-center {
  display: flex;
  justify-content: flex-start !important;
  align-items: center !important;
}

.flex-start-end {
  display: flex;
  justify-content: flex-start !important;
  align-items: flex-end !important;
}

.flex-start-stretch {
  display: flex;
  justify-content: flex-start !important;
  align-items: stretch !important;
}

.flex-center-start {
  display: flex;
  justify-content: center !important;
  align-items: flex-start !important;
}

.flex-center-center {
  display: flex;
  justify-content: center !important;
  align-items: center !important;
}

.flex-center-end {
  display: flex;
  justify-content: center !important;
  align-items: flex-end !important;
}

.flex-between-start {
  display: flex;
  justify-content: space-between !important;
  align-items: flex-start !important;
}

.flex-between-center {
  display: flex;
  justify-content: space-between !important;
  align-items: center !important;
}

.flex-between-end {
  display: flex;
  justify-content: space-between !important;
  align-items: flex-end !important;
}

.flex-end-start {
  display: flex;
  justify-content: flex-end !important;
  align-items: flex-start !important;
}

.flex-end-center {
  display: flex;
  justify-content: flex-end !important;
  align-items: center !important;
}

.flex-end-end {
  display: flex;
  justify-content: flex-end !important;
  align-items: flex-end !important;
}

.flex-column-start-start {
  display: flex;
  flex-direction: column;
  justify-content: flex-start !important;
  align-items: flex-start !important;
}

.flex-column-start-center {
  display: flex;
  flex-direction: column;
  justify-content: flex-start !important;
  align-items: center !important;
}

.flex-column-center-center {
  display: flex;
  flex-direction: column;
  justify-content: center !important;
  align-items: center !important;
}

.flex-column-center-start {
  display: flex;
  flex-direction: column;
  justify-content: center !important;
  align-items: flex-start !important;
}

.flex-column-between-end {
  display: flex;
  flex-direction: column;
  justify-content: space-between !important;
  align-items: flex-end !important;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

// grid 布局
.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  // gap: 40px 30px;
  column-gap: 40px;
}

// el-date-picker
.el-date-picker {
  .el-picker-panel__icon-btn {
    margin-top: 0;
  }
}

/* 溢出隐藏 */
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.w100 {
  width: 100%;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.pointer-none {
  pointer-events: none;
}

.pointer {
  cursor: pointer !important;
}

// 下划线
.underline {
  text-decoration: underline;
}

.border-color-grey {
  border-color: var(--border-1);
}

.display-none {
  display: none;
}


.problem-type-tag {
  display: inline-block;
  padding: 3px 16px;
  border-radius: 20px;
  color: #fff;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

// 问题分类背景色类
.problem-type-food-safety {
  background-color: #40CD00;
}

.problem-type-meal-funding {
  background-color: #FFA200;
}

.problem-type-external-training {
  background-color: #F5274B;
}

.problem-type-textbook-ordering {
  background-color: #1F6CFF;
}

.problem-type-uniform-procurement {
  background-color: #49CBFF;
}

.problem-type-other {
  background-color: #FFCE00;
}


// 状态标签基础样式
.status-tag {
  display: inline-block;
  padding: 3px 24px;
  border-radius: 20px;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
}

// 状态样式类
.status-pending-assignment {
  color: #FF9200;
  background-color: #FEF5EB;
}

.status-pending-processing {
  color: #1F6CFF;
  background-color: #E9F0FF;
}

.status-pending-review {
  color: #F7B800;
  background-color: #FEF9EB;
}

.status-review-failed {
  color: #F5274B;
  background-color: #FEEBEC;
}

.status-completed {
  color: #5DC730;
  background-color: #EFF9EC;
}

.status-pending-provincial {
  color: #1F6CFF;
  background-color: #E5F8FF;
}

.status-completed-provincial {
  color: #30C7B8;
  background-color: #ECF9F5;
}

.status-default {
  color: #666;
  background-color: #f5f5f5;
}

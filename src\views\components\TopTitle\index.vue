<script setup>
// 定义组件的 props
defineProps({
  title: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <div class="top-title">
    <div class="title-content">
      {{ title }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.top-title {
  display: flex;
  align-items: center;
  padding: 8px 0;

  .title-content {
    position: relative;
    padding-left: 12px;
    font-size: 16px;
    font-weight: 600;
    color: #2B2C33;
    line-height: 22px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 18px;
      background-color: #1A68A8;
      border-radius: 2px;
    }
  }
}
</style>
